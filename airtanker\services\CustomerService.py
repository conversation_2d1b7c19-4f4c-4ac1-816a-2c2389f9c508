from typing import List, Optional, Dict
from fuzzywuzzy import fuzz

from services.DatabaseService import DatabaseService
from models.NameMatch import NameMatch, Status, Origin, normalize_name


class CustomerService:
    '''
    What this function will do:
        1) Try an exact match between the customer name in the customer_names and the customer name in the active work orders.
            1.1) If exact match is found, return [{customer_id: customer_name}] from the active work orders.
        2) Fuzzy match customers from customer_names to customers in active work orders over 70%
            2.1) If results over 90% == 1, return the highest match [{customer_id: customer_name}] from the active work orders.
            2.2) If results over 70% > 1, return the list of customers.
            2.3) If no results over 70%, return None.
        3) Retrieve all customers from database
            3.1) Try an exact match between the customer name in the customer_names and the customer name in the database.
            3.2) Fuzzy match results over 90% == 1, return the highest match [{customer_id: customer_name}] from the database.
            3.3) If results over 70% > 1, return the list of customers.
            3.4) If no results over 70%, return None.

    How the data is expected:
        active_wos_customers = [
            { 'customer_id': 101, 'customer_name': 'ACME Corporation' },
            { 'customer_id': 102, 'customer_name': 'Beta Industries, Inc.' },
            { 'customer_id': 103, 'customer_name': 'Gamma & Sons LLC' },
        ]
        This also applies to the database customers expected input, so we remap after retrieving from the database.

    Example customer_names you want to match:
        customer_names = [
            "Acme Corp",         # Close to "ACME Corporation"
            "Beta Ind.",         # Close to "Beta Industries, Inc."
            "Delta Co"           # Not in active WOs
        ]
    '''

    def __init__(self, database_service: Optional[DatabaseService] = None):
        self.database_service = database_service

    def match_customers(self, customer_names: List[str], active_wos_customers: List[Dict[str, any]]) -> List[NameMatch]:
        """
        Match customer names against active work orders and database customers.
        
        Args:
            customer_names: List of customer names to match
            active_wos_customers: List of dicts with customer_id and customer_name from active work orders
            database_service: Service to query database customers
            
        Returns:
            List of NameMatch objects with matching results
        """
        results = []
        
        for customer_name in customer_names:
            match_result = self._match_single_customer(
                customer_name, 
                active_wos_customers, 
            )
            results.append(match_result)
        
        return results

    def _match_single_customer(self, customer_name: str, active_wos_customers: List[Dict[str, any]]) -> NameMatch:
        """Match a single customer name through the multi-step process."""
        
        # Step 1: Exact match in active work orders
        exact_match = self._find_exact_match(customer_name, active_wos_customers)
        if exact_match:
            return NameMatch(
                name=customer_name,
                status=Status.MATCHED,
                origin=Origin.ACTIVE_WO,
                ids=[exact_match['customer_id']]
            )
        
        # Step 2: Fuzzy match in active work orders
        fuzzy_result = self._fuzzy_match_customers(customer_name, active_wos_customers, Origin.ACTIVE_WO)
        if fuzzy_result:
            return fuzzy_result
        
        # Step 3: Search in database
        try:
            all_db_customers = self.database_service.get_all_customers()
            remapped_db_customers = [
                {'customer_id': customer['CustomerID'], 'customer_name': customer['CustomerName']}
                for customer in all_db_customers
            ]
            
            # Step 3.1: Exact match in database
            exact_db_match = self._find_exact_match(customer_name, remapped_db_customers)
            if exact_db_match:
                return NameMatch(
                    name=customer_name,
                    status=Status.MATCHED,
                    origin=Origin.DATABASE,
                    ids=[exact_db_match['customer_id']]
                )
            
            # Step 3.2-3.4: Fuzzy match in database
            db_fuzzy_result = self._fuzzy_match_customers(customer_name, remapped_db_customers, Origin.DATABASE)
            if db_fuzzy_result:
                return db_fuzzy_result
                
        except Exception as e:
            # Handle database errors gracefully
            print(f"Database error while matching {customer_name}: {e}")
        
        # No matches found
        return NameMatch(
            name=customer_name,
            status=Status.NOT_FOUND,
            origin=None,
            ids=None
        )

    def _find_exact_match(self, customer_name: str, customers: List[Dict[str, any]]) -> Optional[Dict[str, any]]:
        """Find exact match (case-insensitive) in customer list."""
        for customer in customers:
            if customer['customer_name'].lower() == customer_name.lower():
                return customer
        return None

    def _fuzzy_match_customers(self, customer_name: str, customers: List[Dict[str, any]], origin: Origin) -> Optional[NameMatch]:
        """
        Perform fuzzy matching on customer list.
        
        Returns:
            NameMatch with MATCHED status if single match >90%
            NameMatch with AMBIGUOUS status if multiple matches >70%
            None if no matches >70%
        """
        matches = []
        
        for customer in customers:
            similarity = fuzz.ratio(
                customer_name.lower(), 
                customer['customer_name'].lower()
            )
            
            if similarity > 70:
                matches.append({
                    'customer': customer,
                    'similarity': similarity
                })
        
        if not matches:
            return None
        
        # Sort by similarity (highest first)
        matches.sort(key=lambda x: x['similarity'], reverse=True)
        
        # Check for matches over 90%
        high_confidence_matches = [m for m in matches if m['similarity'] > 90]
        
        if len(high_confidence_matches) == 1:
            return NameMatch(
                name=customer_name,
                status=Status.MATCHED,
                origin=origin,
                ids=[high_confidence_matches[0]['customer']['customer_id']]
            )
        
        # Multiple matches over 70% (ambiguous)
        if len(matches) > 1:
            return NameMatch(
                name=customer_name,
                status=Status.AMBIGUOUS,
                origin=origin,
                ids=[m['customer']['customer_id'] for m in matches]
            )
        
        # Single match over 70% but under 90%
        return NameMatch(
            name=customer_name,
            status=Status.AMBIGUOUS,
            origin=origin,
            ids=[matches[0]['customer']['customer_id']]
        )